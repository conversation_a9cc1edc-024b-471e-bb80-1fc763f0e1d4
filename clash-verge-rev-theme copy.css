/* ========================================
   Clash Verge Rev 优化主题 - 浅色/深色双模式
   ======================================== */

   section {
    background-color: rgba(255, 255, 255, 0) !important;
}


html {
  font-size: 16px;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --background-color: #f0f0f000 !important;
}

.base-container {
    background-color: #f0f0f000 !important;
}


.MuiTypography-root {
    background-color: rgba(0, 0, 0, 0) !important;
}


/* MuiBox 基础样式 - 全局透明背景 */
.MuiBox-root {
    background-color: rgba(255, 255, 255, 0) !important;
}


/* 全局背景图 - 透明度0.8 */
.css-1li7dvq {
    position: relative;
    background-color: rgba(255, 255, 255, 0) !important;
}

.css-1li7dvq::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 1;
    z-index: -2;
}

.css-1li7dvq::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: -1;
}

.css-l3ykv8 {
    position: relative;
    background-color: rgba(255, 255, 255, 0) !important;
    color: rgb(239, 239, 239) !important;
}

.css-l3ykv8::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://img.paulzzh.com/touhou/random?site=all&size=pc') !important;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 1;
    z-index: -2;
}

.css-l3ykv8::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: -1;
}


/* 主题模式和变量定义 */
:root {
  /* 浅色模式变量 */

  --border-light: rgba(0, 0, 0, 0.08);
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
  
  
  /* 布局变量 */
  --base-spacing: 1.5rem;
  --grid-gap: 1.2rem;
  --card-min-width: 280px;
  --sidebar-width: 280px;
  --header-height: 64px;
  --border-radius: 12px;
  --border-radius-small: 8px;
  --border-radius-large: 16px;
  
  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 768px;
  --breakpoint-md: 1024px;
  --breakpoint-lg: 1200px;
  --breakpoint-xl: 1440px;
  
}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]):not(.light-mode) {

    --text-secondary: rgba(255, 255, 255, 0.9);
    --text-tertiary: rgba(255, 255, 255, 0.7);
    --text-inverse: rgba(33, 37, 41, 0.95);

    --border-light: rgba(255, 255, 255, 0.08);

    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.5);

  }
}


/* 左侧导航栏 */
.layout__left, .sidebar, .nav-sidebar {
  flex: 0 0 var(--sidebar-width);
  background: var(--background-glass);
  backdrop-filter: var(--blur-medium);
  border-right: 1px solid var(--border-light);
  padding: var(--base-spacing);
  min-width: 280px;
  max-width: 360px;
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 10;
}


/* ========================================
   响应式网格系统
   ======================================== */

/* 响应式网格容器 */
.responsive-grid, .grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
  gap: var(--grid-gap);
  padding: var(--grid-gap);
  width: 100%;
  align-items: start;
}

/* 网格项目 */
.grid-item {
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 菜单系统网格 */
.the-menu, .menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--base-spacing);
  margin: var(--base-spacing) 0;
}


/* ========================================
   Material-UI 组件优化
   ======================================== */



/* 按钮组件 */
.MuiButton-root, .MuiButtonBase-root {
  border: 1px solid var(--border-light) !important;
  backdrop-filter: var(--blur-light);
  border-radius: var(--border-radius-small) !important;

  box-shadow: var(--shadow-light) !important;
}


/* 开关组件 - 统一样式，不受主题影响 */


/* ========================================
   Gemini 风格渐变色和动态悬停效果
   ======================================== */

/* Gemini 风格 CSS 变量定义 - 基于参考文件 */
:root {
  --gemini-color-logo-gradient: linear-gradient(90deg,
      #2079fe 0%,
      #098efb 33.53%,
      #ad89eb 70%,
      #ef4e5e 100%);
  --gemini-branding-text-gradient: linear-gradient(90deg, #217BFE 0%, #078EFB 33.53%, #AC87EB 67.74%, #EE4D5D 100%);
  --gemini-enhanced-text-gradient: linear-gradient(90deg, #1a5fd1 0%, #0670c7 33.53%, #8b6bc2 67.74%, #c73e4e 100%);
  --gemini-color-gemini-blue: #368EFE;
  --gemini-color-gemini-cyan: #4FABFF;
  --gemini-color-gemini-light-blue: #B1C5FF;


}

/* 深色模式变量 */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]):not(.light-mode) {
    --custom-main-background: rgb(30, 31, 32, 0.3);
    --custom-side-background: rgb(30, 31, 32, 0.3);

    --custom-card-bg: rgba(30, 31, 32, 0.4);
  }
}

/* 浅色模式变量 */
:root[data-theme="light"], .light-mode {
  --custom-main-background: rgb(255, 255, 255, 0.6);
  --custom-side-background: rgb(240, 244, 249, 0.6);

  --custom-card-bg: rgba(255, 255, 255, 0.4);
}



/* ========================================
   Gemini 风格渐变色和悬停效果 - 基于参考文件
   ======================================== */

/* Gemini 风格 CSS 变量定义 - 完全基于参考文件 */
:root {
  --gemini-color-logo-gradient: linear-gradient(90deg,
      #2079fe 0%,
      #098efb 33.53%,
      #ad89eb 70%,
      #ef4e5e 100%);
  --gemini-color-white: #ffffff;
  --gemini-color-black: #000000;
  --gemini-color-grey-50: #f8f9fa;
  --gemini-color-grey-100: #f1f3f4;
  --gemini-color-grey-200: #e8eaed;
  --gemini-color-grey-300: #dadce0;
  --gemini-color-grey-400: #bdc1c6;
  --gemini-color-grey-500: #9aa0a6;
  --gemini-color-grey-600: #80868b;
  --gemini-color-grey-700: #5f6368;
  --gemini-color-grey-800: #3c4043;
  --gemini-color-grey-900: #202124;
  --gemini-color-blue-50: #e8f0fe;
  --gemini-color-blue-100: #d2e3fc;
  --gemini-color-blue-200: #aecbfa;
  --gemini-color-blue-300: #8ab4f8;
  --gemini-color-blue-400: #669df6;
  --gemini-color-blue-500: #4285f4;
  --gemini-color-blue-600: #1a73e8;
  --gemini-color-blue-700: #1967d2;
  --gemini-color-blue-800: #185abc;
  --gemini-color-blue-900: #174ea6;
  --gemini-color-red-50: #fce8e6;
  --gemini-color-red-100: #fad2cf;
  --gemini-color-red-200: #f6aea9;
  --gemini-color-red-300: #f28b82;
  --gemini-color-red-400: #ee675c;
  --gemini-color-red-500: #ea4335;
  --gemini-color-red-600: #d93025;
  --gemini-color-red-700: #c5221f;
  --gemini-color-red-800: #b31412;
  --gemini-color-red-900: #a50e0e;
  --gemini-color-green-50: #e6f4ea;
  --gemini-color-green-100: #ceead6;
  --gemini-color-green-200: #a8dab5;
  --gemini-color-green-300: #81c995;
  --gemini-color-green-400: #5bb974;
  --gemini-color-green-500: #34a853;
  --gemini-color-green-600: #137333;
  --gemini-color-green-700: #0d652d;
  --gemini-color-green-800: #0b5394;
  --gemini-color-green-900: #0a5d00;
  --gemini-color-yellow-50: #fef7e0;
  --gemini-color-yellow-100: #feefc3;
  --gemini-color-yellow-200: #fde047;
  --gemini-color-yellow-300: #fcd34d;
  --gemini-color-yellow-400: #fbbf24;
  --gemini-color-yellow-500: #f59e0b;
  --gemini-color-yellow-600: #d97706;
  --gemini-color-yellow-700: #b45309;
  --gemini-color-yellow-800: #92400e;
  --gemini-color-yellow-900: #78350f;
  --gemini-color-gemini-blue: #368efe;
  --gemini-color-gemini-cyan: #4fabff;
  --gemini-color-gemini-light-blue: #b1c5ff;
  --gemini-color-blue: #368efe;
  --gemini-color-purple-100: #ac87eb;
  --gemini-color-red-200: #ee4d5d;
  --gemini-color-green-800: #137333;
  --gemini-color-blue-800: #185ABC;
  --gemini-color-blue-gradient: linear-gradient(61deg, #64b8fb 6.28%, #217bfe 76.97%);
  --gemini-color-pink-gradient: linear-gradient(90deg, #a485fa -104.88%, var(--gemini-color-red-200) 198.78%);
  --gemini-color-logo-gradient: linear-gradient(90deg, #217bfe 0%, #078efb 33.53%, #ac87eb 70%, #ee4d5d 100%);
  --gemini-color-primary-button-gradient: linear-gradient(52deg, #0844ff 11.5%, #64b8fb 129.52%);
  --gemini-color-chart-gradient: linear-gradient(105deg, #446eff 18.71%, #2e96ff 49.8%, #b1c5ff 90.55%);
  --gemini-color-foreground: var(--gemini-color-white);
  --gemini-color-background: var(--gemini-color-grey-900);
  --gemini-branding-button-gradient: linear-gradient(15deg, #217BFE 1.02%, #078EFB 28.51%, #A190FF 80.14%, #BD99FE 102.85%);
  --gemini-branding-text-gradient: linear-gradient(90deg, #217BFE 0%, #078EFB 33.53%, #AC87EB 67.74%, #EE4D5D 100%);
  --gemini-enhanced-text-gradient: linear-gradient(90deg, #1a5fd1 0%, #0670c7 33.53%, #8b6bc2 67.74%, #c73e4e 100%);
  --gemini-gradient-linear-colors: var(--gemini-color-gemini-blue) 5.96%, var(--gemini-color-gemini-cyan) 56.89%, var(--gemini-color-gemini-light-blue) 93.53%;
  --gemini-gradient-linear: linear-gradient(53deg, #0260FF 9.29%, #40A2FF 48.23%, #A8BEFF 82.56%);
  --gemini-text-gradient-light-blue: linear-gradient(69deg, #AABDF4 16.42%, #FFF 77.56%, #A8BEFF 124.91%);

  /* Gemini风格多彩文字渐变 - 基于参考文件颜色 */
  --gemini-multi-text-gradient: linear-gradient(90deg,
    #217bfe 0%,     /* Gemini蓝 */
    #078efb 12.5%,  /* 深蓝 */
    #4fabff 25%,    /* 青蓝 */
    #64b8fb 37.5%,  /* 浅蓝 */
    #ac87eb 50%,    /* 紫色 */
    #a485fa 62.5%,  /* 淡紫 */
    #bd99fe 75%,    /* 粉紫 */
    #ee4d5d 87.5%,  /* 红色 */
    #ef4e5e 100%);  /* 深红 */

  /* Gemini风格扩展渐变 - 更多颜色变化 */
  --gemini-extended-gradient: linear-gradient(90deg,
    #0260ff 0%,     /* 深蓝 */
    #217bfe 10%,    /* Gemini蓝 */
    #40a2ff 20%,    /* 中蓝 */
    #64b8fb 30%,    /* 浅蓝 */
    #a8beff 40%,    /* 淡蓝 */
    #b1c5ff 50%,    /* 极淡蓝 */
    #ac87eb 60%,    /* 紫色 */
    #a485fa 70%,    /* 淡紫 */
    #bd99fe 80%,    /* 粉紫 */
    #ee4d5d 90%,    /* 红色 */
    #ef4e5e 100%);  /* 深红 */

  /* 流动卡片悬停渐变 - Gemini风格多彩 */
  --gemini-flowing-gradient: linear-gradient(90deg,
    rgba(33, 123, 254, 0.8) 0%,     /* Gemini蓝 */
    rgba(7, 142, 251, 0.9) 5%,      /* 深蓝 */
    rgba(79, 171, 255, 0.8) 10%,    /* 青蓝 */
    rgba(100, 184, 251, 0.7) 15%,   /* 浅蓝 */
    rgba(168, 190, 255, 0.6) 20%,   /* 淡蓝 */
    rgba(177, 197, 255, 0.5) 25%,   /* 极淡蓝 */
    rgba(172, 135, 235, 0.6) 30%,   /* 紫色 */
    rgba(164, 133, 250, 0.7) 35%,   /* 淡紫 */
    rgba(189, 153, 254, 0.8) 40%,   /* 粉紫 */
    rgba(238, 77, 93, 0.9) 45%,     /* 红色 */
    rgba(239, 78, 94, 0.8) 50%,     /* 深红 */
    rgba(238, 77, 93, 0.9) 55%,     /* 红色 */
    rgba(189, 153, 254, 0.8) 60%,   /* 粉紫 */
    rgba(164, 133, 250, 0.7) 65%,   /* 淡紫 */
    rgba(172, 135, 235, 0.6) 70%,   /* 紫色 */
    rgba(177, 197, 255, 0.5) 75%,   /* 极淡蓝 */
    rgba(168, 190, 255, 0.6) 80%,   /* 淡蓝 */
    rgba(100, 184, 251, 0.7) 85%,   /* 浅蓝 */
    rgba(79, 171, 255, 0.8) 90%,    /* 青蓝 */
    rgba(7, 142, 251, 0.9) 95%,     /* 深蓝 */
    rgba(33, 123, 254, 0.8) 100%);  /* 回到Gemini蓝 */
}

/* 深色模式变量 */
.dark,
[data-theme="dark"] {
  --gemini-color-foreground: var(--gemini-color-white);
  --gemini-color-background: var(--gemini-color-grey-900);
}

/* 浅色模式变量 */
:root[data-theme="light"], .light-mode {
  --gemini-color-foreground: var(--gemini-color-black);
  --gemini-color-background: var(--gemini-color-white);
}

/* 标题文字渐变效果 - 完全基于参考文件的选择器 */
.main .title.text-lg.leading-\[32px\],
.main .h-\[32px\].leading-\[32px\],
.main .text-default-500 .ml-2,
.font-bold {
  background-image: var(--gemini-color-logo-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Side渐变文字增强 - 更深更清晰的渐变 */
.side .title.text-lg.leading-\[32px\],
.side .h-\[32px\].leading-\[32px\],
.side .text-default-500 .ml-2,
.side .font-bold {
  background-image: var(--gemini-enhanced-text-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  font-weight: bold !important;
}

/* 左侧导航文字渐变效果 - 使用Gemini风格多彩渐变 */
.MuiListItemText-primary {
  background-image: var(--gemini-multi-text-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  font-weight: bold !important;
}

/* #layout1 SVG logo文字渐变效果 - 使用CSS文字渐变 */
#layout1 {
  background-image: var(--gemini-extended-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
  font-weight: bold !important;
  transition: all 0.3s ease !important;
}

/* data-tauri-drag-region 元素文字渐变效果 */
[data-tauri-drag-region] {
  background-image: var(--gemini-extended-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
  font-weight: bold !important;
}

/* 设置页面文字渐变效果 - MuiBox-root css-1i24pk4 */
.css-1i24pk4,
.css-1i24pk4 span {
  background-image: var(--gemini-extended-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
  font-weight: bold !important;
}

/* 悬停时的logo效果 */
#layout1:hover {
  filter: brightness(1.2) saturate(1.3) !important;
  transform: scale(1.05) !important;
}

/* 右侧所有文字渐变效果 - 覆盖更多元素 */
.main span,
.side span,
.main .MuiTypography-root,
.side .MuiTypography-root,
.main .text-sm,
.side .text-sm,
.main .text-base,
.side .text-base,
.main .text-lg,
.side .text-lg,
.main .text-xl,
.side .text-xl,
.main .font-medium,
.side .font-medium,
.main .font-semibold,
.side .font-semibold,
.main .leading-6,
.side .leading-6,
.main .leading-7,
.side .leading-7,
.main .leading-8,
.side .leading-8 {
  background-image: var(--gemini-multi-text-gradient) !important;
  background-clip: text !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  font-weight: bold !important;
}

/* 强化流动渐变动画 - 确保明显的从左到右效果 */
@keyframes flowingGradient {
  0% {
    background-position: -500% center;
    opacity: 0.2;
  }
  25% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.8;
  }
  75% {
    opacity: 0.6;
  }
  100% {
    background-position: 500% center;
    opacity: 0.2;
  }
}

/* 更强烈的流动动画 - 适用于所有卡片 */
@keyframes flowingGradientIntense {
  0% {
    background-position: -600% center;
    opacity: 0.1;
  }
  20% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.9;
  }
  80% {
    opacity: 0.7;
  }
  100% {
    background-position: 600% center;
    opacity: 0.1;
  }
}

/* 替代方案：使用transform动画 */
@keyframes shimmerEffect {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 流动多彩卡片悬停效果 - 根据宽度调整速度 */
.main .bg-primary-foreground,
.side .bg-primary-foreground,
.main .bg-foreground,
.side .bg-foreground,
.main .bg-content1,
.side .bg-content1,
.main .bg-default,
.side .bg-default,
button,
.MuiButton-root,
.MuiListItemButton-root {
  position: relative !important;
  transition: all 0.3s ease !important;
  overflow: hidden !important;
}

/* 流动渐变背景层 - 确保元素有相对定位 */
.main .bg-primary-foreground,
.side .bg-primary-foreground,
.main .bg-foreground,
.side .bg-foreground,
.main .bg-content1,
.side .bg-content1,
.main .bg-default,
.side .bg-default,
button,
.MuiButton-root,
.MuiListItemButton-root {
  position: relative !important;
  overflow: hidden !important;
}

.main .bg-primary-foreground::before,
.side .bg-primary-foreground::before,
.main .bg-foreground::before,
.side .bg-foreground::before,
.main .bg-content1::before,
.side .bg-content1::before,
.main .bg-default::before,
.side .bg-default::before,
button::before,
.MuiButton-root::before,
.MuiListItemButton-root::before,
/* 添加更多通用选择器确保覆盖所有卡片 */
div[class*="css-"]::before,
li[class*="MuiListItem"]::before,
div[class*="MuiBox"]::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: var(--gemini-flowing-gradient) !important;
  background-size: 1000% 100% !important;
  background-position: -500% center !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
  z-index: 0 !important;
  pointer-events: none !important;
  border-radius: inherit !important;
}

/* 所有卡片悬停效果 - 强化流动动画 */
.main .bg-primary-foreground:hover::before,
.side .bg-primary-foreground:hover::before,
.main .bg-foreground:hover::before,
.side .bg-foreground:hover::before,
.main .bg-content1:hover::before,
.side .bg-content1:hover::before,
.main .bg-default:hover::before,
.side .bg-default:hover::before,
button:hover::before,
.MuiButton-root:hover::before,
.MuiListItemButton-root:hover::before,
/* 添加更多通用悬停选择器 */
div[class*="css-"]:hover::before,
li[class*="MuiListItem"]:hover::before,
div[class*="MuiBox"]:hover::before {
  opacity: 1 !important;
  animation: flowingGradientIntense 3s ease-in-out infinite !important;
}

/* 特殊的测试卡片和其他重要卡片 */
.MuiListItemButton-root:hover::before,
li:hover::before,
button:hover::before {
  opacity: 1 !important;
  animation: flowingGradient 2s linear infinite !important;
}

/* 悬停时的整体效果 */
.main .bg-primary-foreground:hover,
.side .bg-primary-foreground:hover,
.main .bg-foreground:hover,
.side .bg-foreground:hover,
.main .bg-content1:hover,
.side .bg-content1:hover,
.main .bg-default:hover,
.side .bg-default:hover,
button:hover,
.MuiButton-root:hover,
.MuiListItemButton-root:hover {
  transform: translateY(-1px) !important;
  box-shadow:
    0 4px 20px rgba(33, 123, 254, 0.2),
    0 0 0 1px rgba(33, 123, 254, 0.1) !important;
}

/* 自定义样式结束 */

